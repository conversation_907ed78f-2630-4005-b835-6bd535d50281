import React from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
} from 'recharts';
import { cn } from '@/lib/utils';

export interface ChartDataPoint {
  name: string;
  value: number;
  [key: string]: any;
}

export interface InteractiveChartProps {
  data: ChartDataPoint[];
  type: 'line' | 'area' | 'bar' | 'pie';
  height?: number;
  className?: string;
  color?: string;
  gradient?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  animate?: boolean;
}

// Semantic color palette for charts - meaningful colors for different data types
const COLORS = [
  'hsl(142 76% 36%)',             // Green - positive/success
  'hsl(346 87% 43%)',             // Red - negative/error
  'hsl(48 96% 53%)',              // Yellow/Orange - warning/neutral
  'hsl(217 91% 60%)',             // Blue - information/cool
  'hsl(262 83% 58%)',             // Purple - premium/special
  'hsl(24 70% 56%)',              // Orange - energy/action
  'hsl(197 71% 52%)',             // Cyan - fresh/modern
  'hsl(var(--primary))',          // Brand color as fallback
  'hsl(0 0% 60%)',                // Gray - neutral/other
  'hsl(var(--muted-foreground))', // Muted - least important
];

// Smart color assignment based on data names/content
const getSemanticColor = (name: string, index: number): string => {
  const lowerName = name.toLowerCase();

  // Sentiment-based colors
  if (lowerName.includes('positive') || lowerName.includes('good') || lowerName.includes('excellent') || lowerName.includes('great')) {
    return 'hsl(142 76% 36%)'; // Green
  }
  if (lowerName.includes('negative') || lowerName.includes('bad') || lowerName.includes('poor') || lowerName.includes('terrible')) {
    return 'hsl(346 87% 43%)'; // Red
  }
  if (lowerName.includes('neutral') || lowerName.includes('mixed') || lowerName.includes('average')) {
    return 'hsl(48 96% 53%)'; // Yellow/Orange
  }

  // Platform-based colors
  if (lowerName.includes('instagram') || lowerName.includes('ig')) {
    return 'hsl(262 83% 58%)'; // Purple (Instagram brand)
  }
  if (lowerName.includes('facebook') || lowerName.includes('fb')) {
    return 'hsl(217 91% 60%)'; // Blue (Facebook brand)
  }
  if (lowerName.includes('twitter') || lowerName.includes('x')) {
    return 'hsl(197 71% 52%)'; // Cyan/Light blue
  }
  if (lowerName.includes('tiktok')) {
    return 'hsl(346 87% 43%)'; // Red/Pink (TikTok brand)
  }
  if (lowerName.includes('youtube')) {
    return 'hsl(346 87% 43%)'; // Red (YouTube brand)
  }
  if (lowerName.includes('linkedin')) {
    return 'hsl(217 91% 60%)'; // Blue (LinkedIn brand)
  }

  // Fallback to color palette
  return COLORS[index % COLORS.length];
};

export function InteractiveChart({
  data,
  type,
  height = 300,
  className,
  color = 'hsl(var(--primary))',
  gradient = false,
  showGrid = true,
  showTooltip = true,
  animate = true,
}: InteractiveChartProps) {
  const renderChart = () => {
    const commonProps = {
      data,
      margin: { top: 5, right: 30, left: 20, bottom: 5 },
    };

    switch (type) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" className="opacity-30" />}
            <XAxis 
              dataKey="name" 
              className="text-xs"
              tick={{ fontSize: 12 }}
            />
            <YAxis 
              className="text-xs"
              tick={{ fontSize: 12 }}
            />
            {showTooltip && (
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--background))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                }}
              />
            )}
            <Line
              type="monotone"
              dataKey="value"
              stroke={color}
              strokeWidth={2}
              dot={{ fill: color, strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: color, strokeWidth: 2 }}
              animationDuration={animate ? 1000 : 0}
            />
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            {gradient && (
              <defs>
                <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={color} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={color} stopOpacity={0.1} />
                </linearGradient>
              </defs>
            )}
            {showGrid && <CartesianGrid strokeDasharray="3 3" className="opacity-30" />}
            <XAxis 
              dataKey="name" 
              className="text-xs"
              tick={{ fontSize: 12 }}
            />
            <YAxis 
              className="text-xs"
              tick={{ fontSize: 12 }}
            />
            {showTooltip && (
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--background))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                }}
              />
            )}
            <Area
              type="monotone"
              dataKey="value"
              stroke={color}
              fillOpacity={1}
              fill={gradient ? "url(#colorGradient)" : color}
              animationDuration={animate ? 1000 : 0}
            />
          </AreaChart>
        );

      case 'bar':
        return (
          <BarChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" className="opacity-30" />}
            <XAxis 
              dataKey="name" 
              className="text-xs"
              tick={{ fontSize: 12 }}
            />
            <YAxis 
              className="text-xs"
              tick={{ fontSize: 12 }}
            />
            {showTooltip && (
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--background))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                }}
              />
            )}
            <Bar
              dataKey="value"
              fill={color}
              radius={[4, 4, 0, 0]}
              animationDuration={animate ? 1000 : 0}
            />
          </BarChart>
        );

      case 'pie':
        return (
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) =>
                percent > 0.05 ? `${name} ${(percent * 100).toFixed(0)}%` : ''
              }
              outerRadius={Math.min(height * 0.35, 120)}
              innerRadius={0}
              fill="hsl(var(--primary))"
              dataKey="value"
              animationDuration={animate ? 1000 : 0}
              stroke="hsl(var(--background))"
              strokeWidth={2}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={getSemanticColor(entry.name, index)}
                  stroke="hsl(var(--background))"
                  strokeWidth={2}
                />
              ))}
            </Pie>
            {showTooltip && (
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--background))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                  boxShadow: 'var(--shadow-md)',
                }}
                labelStyle={{
                  color: 'hsl(var(--foreground))',
                  fontWeight: '500',
                }}
                formatter={(value: any, name: string) => [
                  `${value}`,
                  name
                ]}
              />
            )}
          </PieChart>
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn('w-full', className)} style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        {renderChart()}
      </ResponsiveContainer>
    </div>
  );
}
