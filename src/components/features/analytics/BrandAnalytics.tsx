import { useState } from "react";
import { AnalyticsCard } from "./AnalyticsCard";
import { InteractiveChart } from "@/components/ui/interactive-chart";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ANALYTICS_METRICS, ANALYTICS_ICONS } from "@/constants/analytics";
import { Calendar, Download, Filter, TrendingUp, Users, MessageSquare, Clock } from "lucide-react";

// Mock data for detailed analytics
const responseTimeData = [
  { name: 'Mon', value: 2.3 },
  { name: 'Tu<PERSON>', value: 1.8 },
  { name: 'Wed', value: 2.1 },
  { name: 'Thu', value: 1.5 },
  { name: 'Fri', value: 2.7 },
  { name: 'Sat', value: 3.2 },
  { name: '<PERSON>', value: 2.9 },
];

const sentimentData = [
  { name: 'Positive', value: 65 },
  { name: 'Neutral', value: 25 },
  { name: 'Negative', value: 10 },
];

const platformData = [
  { name: 'Twitter', value: 45 },
  { name: 'LinkedIn', value: 30 },
  { name: 'Facebook', value: 15 },
  { name: 'Instagram', value: 10 },
];

export function BrandAnalytics() {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');

  const analyticsData = ANALYTICS_METRICS.map((metric, index) => {
    const IconComponent = ANALYTICS_ICONS[index];
    return {
      ...metric,
      icon: <IconComponent className="w-5 h-5 text-primary" />
    };
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-foreground mb-2">Brand Analytics</h2>
          <p className="text-muted-foreground">
            AI performance metrics and brand response insights
          </p>
        </div>

        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-muted-foreground" />
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 text-sm border rounded-md bg-background"
            >
              <option value="24h">Last 24 hours</option>
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
            </select>
          </div>

          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {analyticsData.map((metric, index) => (
          <AnalyticsCard key={index} {...metric} />
        ))}
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="sentiment">Sentiment</TabsTrigger>
          <TabsTrigger value="platforms">Platforms</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-foreground">Response Time Trend</h3>
                <Badge variant="outline">
                  <Clock className="w-3 h-3 mr-1" />
                  Avg 2.3h
                </Badge>
              </div>
              <InteractiveChart
                data={responseTimeData}
                type="line"
                height={200}
                color="hsl(var(--primary))"
              />
            </Card>

            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-foreground">Sentiment Distribution</h3>
                <Badge variant="outline">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  65% Positive
                </Badge>
              </div>
              <InteractiveChart
                data={sentimentData}
                type="pie"
                height={200}
              />
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card className="p-6">
            <h3 className="font-semibold text-foreground mb-4">AI Response Performance</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-foreground">94.2%</div>
                <div className="text-sm text-muted-foreground">Accuracy Rate</div>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-foreground">1.8s</div>
                <div className="text-sm text-muted-foreground">Avg Generation Time</div>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-foreground">87%</div>
                <div className="text-sm text-muted-foreground">Approval Rate</div>
              </div>
            </div>
            <InteractiveChart
              data={responseTimeData}
              type="bar"
              height={250}
              color="hsl(var(--accent))"
            />
          </Card>
        </TabsContent>

        <TabsContent value="sentiment" className="space-y-6">
          <Card className="p-6">
            <h3 className="font-semibold text-foreground mb-4">Sentiment Analysis Over Time</h3>
            <InteractiveChart
              data={responseTimeData.map(item => ({ ...item, value: item.value * 20 }))}
              type="area"
              height={300}
              color="hsl(var(--accent))"
              gradient={true}
            />
          </Card>
        </TabsContent>

        <TabsContent value="platforms" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="font-semibold text-foreground mb-4">Platform Distribution</h3>
              <InteractiveChart
                data={platformData}
                type="pie"
                height={250}
              />
            </Card>

            <Card className="p-6">
              <h3 className="font-semibold text-foreground mb-4">Platform Performance</h3>
              <InteractiveChart
                data={platformData}
                type="bar"
                height={250}
                color="hsl(var(--primary))"
              />
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
